//...
//we are importing the express from the modules...
const express = require("express");
//import .env to use environmwnt variable...
require("dotenv").config(); //now env variables are available under process.env Object...
const path = require("path");
const basicRoutes = require("./routes/index"); // basic api endpoints for testing...
const usersRoutes = require("./routes/userRoutes"); // endpoints related to users are here...
const carsRoutes = require("./routes/carRoutes"); // endpoints related to users are here...
const uploadRoutes = require("./routes/uploadRoutes"); // endpoints related to uploads are here...
const chatRoutes = require("./routes/chatRoutes"); // endpoints related to users are here...
const { logger, errorLogger } = require("./middleware/logger");
const cors = require("cors"); // importing the cors package...

// the express module will export a function and that is store in express()...
const server = express(); // we are getting an instance of express()...

// setting up the port for the server...
const PORT = process.env.PORT;

// cors middleware to allow request from all the domains...
server.use(cors());

//simple route to check...
server.use(logger);
server.use(express.json()); // For parsing application/json

// Serve static files from uploads directory
server.use("/uploads", express.static(path.join(__dirname, "uploads")));

server.use("/", basicRoutes);
server.use("/users", usersRoutes); // api related users are handled by userRoutes
server.use("/cars", carsRoutes); // api related cars are handled by userRoutes
server.use("/api", uploadRoutes); // api related uploads are handled by uploadRoutes

server.use("/chats", chatRoutes);
// error logger should always be after the routes...
server.use(errorLogger);

// this is the start point of the server...
server.listen(PORT, () => {
  console.log(
    `server is live on the PORT: ${PORT}, URL: http://localhost:${PORT}`
  );
}); //listen method will make the server to go online...
