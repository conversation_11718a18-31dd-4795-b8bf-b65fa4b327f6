export const sampleApplication = [
  {
    id: 1,
    date: "2025-01-01",
    car_brand: "Toyota",
    model_year: 2019,
    owner_address: "New York",
    price: 1200000,
    status: "Available",
  },
  {
    id: 2,
    date: "2025-01-02",
    car_brand: "Honda",
    model_year: 2021,
    owner_address: "Los Angeles",
    price: 1400000,
    status: "Sold",
  },
  {
    id: 3,
    date: "2025-01-03",
    car_brand: "Ford",
    model_year: 2018,
    owner_address: "Chicago",
    price: 1150000,
    status: "Available",
  },
  {
    id: 4,
    date: "2025-01-04",
    car_brand: "Chevrolet",
    model_year: 2020,
    owner_address: "Houston",
    price: 1300000,
    status: "Available",
  },
  {
    id: 5,
    date: "2025-01-05",
    car_brand: "Nissan",
    model_year: 2022,
    owner_address: "Phoenix",
    price: 1500000,
    status: "Sold",
  },
  {
    id: 6,
    date: "2025-01-06",
    car_brand: "BMW",
    model_year: 2020,
    owner_address: "Philadelphia",
    price: 2200000,
    status: "Available",
  },
  {
    id: 7,
    date: "2025-01-07",
    car_brand: "Mercedes",
    model_year: 2017,
    owner_address: "San Antonio",
    price: 2500000,
    status: "Available",
  },
  {
    id: 8,
    date: "2025-01-08",
    car_brand: "Hyundai",
    model_year: 2021,
    owner_address: "San Diego",
    price: 1400000,
    status: "Sold",
  },
  {
    id: 9,
    date: "2025-01-09",
    car_brand: "Kia",
    model_year: 2023,
    owner_address: "Dallas",
    price: 1600000,
    status: "Available",
  },
  {
    id: 10,
    date: "2025-01-10",
    car_brand: "Audi",
    model_year: 2020,
    owner_address: "San Jose",
    price: 2400000,
    status: "Sold",
  },
  {
    id: 11,
    date: "2025-01-11",
    car_brand: "Volkswagen",
    model_year: 2019,
    owner_address: "Austin",
    price: 1900000,
    status: "Available",
  },
  {
    id: 12,
    date: "2025-01-12",
    car_brand: "Subaru",
    model_year: 2022,
    owner_address: "Jacksonville",
    price: 1700000,
    status: "Sold",
  },
  {
    id: 13,
    date: "2025-01-13",
    car_brand: "Tesla",
    model_year: 2023,
    owner_address: "Fort Worth",
    price: 3500000,
    status: "Available",
  },
  {
    id: 14,
    date: "2025-01-14",
    car_brand: "Mazda",
    model_year: 2018,
    owner_address: "Columbus",
    price: 1300000,
    status: "Available",
  },
  {
    id: 15,
    date: "2025-01-15",
    car_brand: "Jeep",
    model_year: 2020,
    owner_address: "Indianapolis",
    price: 1800000,
    status: "Sold",
  },
  {
    id: 16,
    date: "2025-01-16",
    car_brand: "Volvo",
    model_year: 2019,
    owner_address: "Charlotte",
    price: 2200000,
    status: "Available",
  },
  {
    id: 17,
    date: "2025-01-17",
    car_brand: "Land Rover",
    model_year: 2021,
    owner_address: "San Francisco",
    price: 3000000,
    status: "Sold",
  },
  {
    id: 18,
    date: "2025-01-18",
    car_brand: "Jaguar",
    model_year: 2017,
    owner_address: "Seattle",
    price: 2800000,
    status: "Available",
  },
  {
    id: 19,
    date: "2025-01-19",
    car_brand: "Acura",
    model_year: 2023,
    owner_address: "Denver",
    price: 2000000,
    status: "Sold",
  },
  {
    id: 20,
    date: "2025-01-20",
    car_brand: "Porsche",
    model_year: 2020,
    owner_address: "Washington",
    price: 3200000,
    status: "Available",
  },
];
