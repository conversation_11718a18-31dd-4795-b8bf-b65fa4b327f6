# CarApp UI/UX & Error Handling TODO

## 2. Error Handling & User Notification
- [ ] Implement global error boundary to catch and display errors (show a fallback UI or error screen if the app crashes).
- [ ] Add Android Toast (or similar notification) for transient errors (e.g., no internet connection, failed API calls).
- [ ] Show inline error messages for form validation errors (e.g., invalid input, required fields).
- [ ] Display user-friendly error messages for network/API errors (e.g., "Unable to connect. Please check your internet connection.").
- [ ] Add loading and error states to all async actions (e.g., car list fetch, chat send, image upload).
- [ ] Add a global network status listener to notify users of connectivity changes.

## 3. Empty & Loading States
- [ ] Add visually appealing empty states for lists (e.g., "No cars found", "No chats yet") with icons or illustrations.
    link for icons https://icons.expo.fyi/Index
- [ ] Use skeleton loaders or spinners for loading states.


**Prioritize the most visible and impactful changes first (typography, error notifications, loading/empty states).**

Add more items as needed during implementation.
