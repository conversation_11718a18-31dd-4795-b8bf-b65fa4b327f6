{"cli": {"version": ">= 5.9.1", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug", "image": "ubuntu-22.04-jdk-17-ndk-r21e", "env": {"ANDROID_NDK_HOME": "/opt/android-ndk"}}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "env": {"ANDROID_NDK_HOME": "/opt/android-ndk"}}}, "production": {"autoIncrement": true, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "image": "ubuntu-22.04-jdk-17-ndk-r21e", "env": {"ANDROID_NDK_HOME": "/opt/android-ndk"}}}}, "submit": {"production": {}}}