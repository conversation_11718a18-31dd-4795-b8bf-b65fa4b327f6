{"name": "car-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/inter": "^0.3.0", "@expo-google-fonts/titillium-web": "^0.3.0", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "dayjs": "^1.11.13", "expo": "~52.0.46", "expo-auth-session": "~6.0.3", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.2", "expo-constants": "~17.0.7", "expo-crypto": "~14.0.2", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.5", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-router": "~4.0.20", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.1", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-animatable": "^1.4.0", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.20.2", "react-native-otp-entry": "^1.8.4", "react-native-paper": "^5.13.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-native-wheel-pick": "^1.2.4", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}